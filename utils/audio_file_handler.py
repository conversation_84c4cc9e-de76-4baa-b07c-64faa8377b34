import os
from pathlib import Path
from datetime import datetime

class AudioFileHandler:

    @staticmethod
    def _get_main_dir(test_type, audio_output_dir):
            # 如果是tts类型，则创建一个tts子目录
        if test_type == 'tts':
            main_dir = os.path.join(audio_output_dir, 'tts')
        # 如果是voice类型，则创建一个voice子目录
        elif test_type == 'voice':
            main_dir = os.path.join(audio_output_dir, 'voice')
        else:
            main_dir = audio_output_dir


        os.makedirs(main_dir, exist_ok=True)
        return main_dir

    @staticmethod
    def save_audio_file(audio_data, test_case, test_type, audio_output_dir, turn_id=None):
        """
        保存音频文件
        Args:
            audio_data: 音频数据 (bytes)
            test_case: 测试用例信息
            test_type: 测试类型
            audio_output_dir: 自定义输出目录
            turn_id: 对话轮次ID
           
        Returns:
            str: 保存的音频文件路径
        """
        if not audio_data:
            return None

        # 使用统一的主目录
        save_dir = AudioFileHandler._get_main_dir(test_type, audio_output_dir)

        # 根据轮次生成文件名
        case_id = str(test_case['ID'])

        if test_type == 'tts':
            if turn_id is not None:
                filename = f"tts_{case_id}_{turn_id}.mp3"
            else:
                filename = f"tts_{case_id}_1.mp3"
        elif test_type == 'voice':
            # 如果是voice类型，使用不同的命名方式
            if turn_id is not None:
                # 如果turn_id是字符串并以"捏音"开头，提取数字部分
                if isinstance(turn_id, str) and '捏音' in turn_id:
                    # 尝试提取数字部分
                    import re
                    match = re.search(r'\d+', turn_id)
                    voice_number = match.group(0) if match else '1'
                else:
                    voice_number = turn_id
                filename = f"voice_{case_id}_{voice_number}.mp3"
            else:
                filename = f"voice_{case_id}_1.mp3"
        else:
            # 对于非tts类型，使用原来的命名方式
            if turn_id is not None:
                # 获取当前是第几轮对话
                chat_number = test_case.get('turns', 1)
                # 直接使用 output_case_id_chat_number.mp3 格式
                filename = f"output_{case_id}_{chat_number}.mp3"
            else:
                filename = f"output_{case_id}_1.mp3"

        # 保存音频文件
        file_path = os.path.join(save_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(audio_data)

        print(f"音频文件已保存到: {file_path}")
        return file_path