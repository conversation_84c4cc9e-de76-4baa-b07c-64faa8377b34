#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import argparse
import os
import numpy as np
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side

def merge_sheets(excel_file_path: str):
    """
    Merges 'realtime' and 'voice' sheets into a new 'summary' sheet in the given Excel file.

    For each 'voice' turn in the 'realtime' sheet, it replaces that turn with the corresponding
    multiple rows from the 'voice' sheet based on matching 'ID'.
    """
    try:
        print(f"Merging results into {excel_file_path}")
        # Load the Excel file and its sheets
        xls = pd.ExcelFile(excel_file_path, engine='openpyxl')
        if 'realtime' not in xls.sheet_names:
            print(f"Error: Sheet 'realtime' not found in {excel_file_path}")
            return

        realtime_df = pd.read_excel(xls, sheet_name='realtime', engine='openpyxl')
        if 'ID' in realtime_df.columns:
            realtime_df['ID_str'] = realtime_df['ID'].astype(str)
        else:
            print("Warning: 'ID' column not found in realtime sheet.")
            realtime_df['ID_str'] = "" # Add empty column to prevent key errors

        voice_df = pd.DataFrame() # Initialize as empty
        if 'voice' in xls.sheet_names:
            voice_df = pd.read_excel(xls, sheet_name='voice', engine='openpyxl')
            if 'ID' in voice_df.columns:
                voice_df['ID_str'] = voice_df['ID'].astype(str)
            else:
                print("Warning: 'ID' column not found in voice sheet.")
                voice_df['ID_str'] = "" # Add empty column
        else:
            print("Info: Sheet 'voice' not found. Voice data will not be merged.")

        tts_df = pd.DataFrame() # Initialize as empty
        if 'tts' in xls.sheet_names:
            tts_df = pd.read_excel(xls, sheet_name='tts', engine='openpyxl')
            if 'ID' in tts_df.columns:
                tts_df['ID_str'] = tts_df['ID'].astype(str)
            else:
                print("Warning: 'ID' column not found in tts sheet.")
                tts_df['ID_str'] = "" # Add empty column
        else:
            print("Info: Sheet 'tts' not found. TTS data will not be merged.")

        print(f"Loaded 'realtime' sheet with {len(realtime_df)} rows.")
        if not voice_df.empty:
            print(f"Loaded 'voice' sheet with {len(voice_df)} rows.")
        if not tts_df.empty:
            print(f"Loaded 'tts' sheet with {len(tts_df)} rows.")

        summary_data = []
        tts_turn_counters = {} # Initialize counter for TTS turns per ID

        # Iterate through the realtime_df to build the summary
        for index, rt_row in realtime_df.iterrows():
            is_voice_turn = False
            is_tts_turn = False
            
            turn_type_str = ""
            if 'turns' in rt_row and isinstance(rt_row['turns'], str):
                turn_type_str = str(rt_row['turns']).lower() # Ensure string and lower case
                if turn_type_str.startswith('捏音') or 'voice' in turn_type_str:
                    is_voice_turn = True
                elif turn_type_str.startswith('tts') or '生成式闲聊' in turn_type_str:
                    is_tts_turn = True
            
            merged_for_this_rt_row = False # Flag to check if this rt_row was handled by voice/tts logic

            if is_voice_turn and not voice_df.empty and 'ID_str' in voice_df.columns:
                case_id_str = rt_row['ID_str']
                matching_voice_rows = voice_df[voice_df['ID_str'] == case_id_str]
                
                if not matching_voice_rows.empty:
                    # print(f"  Found {len(matching_voice_rows)} voice entries for ID '{case_id_str}' (realtime row {index+2}). Replacing realtime row with voice entries.")
                    for _, voice_row in matching_voice_rows.iterrows():
                        summary_row_dict = {}
                        # Copy all columns from realtime_df row first
                        for col in realtime_df.columns:
                            if col != 'ID_str': # Exclude temporary ID_str
                                summary_row_dict[col] = rt_row.get(col)
                        
                        # Then, overwrite/add columns from voice_df row
                        for v_col in voice_row.index:
                            if v_col != 'ID_str': # Exclude temporary ID_str
                                summary_row_dict[v_col] = voice_row.get(v_col)
                        
                        # Prioritize realtime sheet for specific columns for overall context
                        summary_row_dict['功能模块'] = rt_row.get('功能模块')
                        summary_row_dict['测试用例'] = rt_row.get('测试用例')
                        summary_row_dict['turns'] = rt_row.get('turns') # 'turns' from rt_row indicates it's a voice turn

                        if 'ID' in voice_row:
                             summary_row_dict['ID'] = voice_row['ID']
                        elif 'ID' in rt_row: # Fallback
                             summary_row_dict['ID'] = rt_row['ID']
                        summary_data.append(summary_row_dict)
                    merged_for_this_rt_row = True
                else:
                    print(f"  ID '{case_id_str}' is a voice turn in realtime (row {index+2}), but no matching ID found in voice sheet. Keeping realtime row.")
            
            elif is_tts_turn and not tts_df.empty and 'ID_str' in tts_df.columns:
                case_id_str = rt_row['ID_str']
                
                # Get the index for which TTS variant to pick for this ID
                tts_idx_for_id = tts_turn_counters.get(case_id_str, 0)
                
                matching_tts_rows_for_id = tts_df[tts_df['ID_str'] == case_id_str]

                if not matching_tts_rows_for_id.empty and tts_idx_for_id < len(matching_tts_rows_for_id):
                    tts_row_to_merge = matching_tts_rows_for_id.iloc[tts_idx_for_id]
                    # print(f"  Found TTS entry for ID '{case_id_str}' (realtime row {index+2}, TTS variant {tts_idx_for_id+1}). Merging TTS data into realtime row.")
                    
                    summary_row_dict = {}
                    # Copy all columns from realtime_df row first
                    for col in realtime_df.columns:
                        if col != 'ID_str': # Exclude temporary ID_str
                            summary_row_dict[col] = rt_row.get(col)
                    
                    # Then, overwrite/add columns from the selected tts_df row
                    for t_col in tts_row_to_merge.index:
                        if t_col != 'ID_str': # Exclude temporary ID_str
                            summary_row_dict[t_col] = tts_row_to_merge.get(t_col)
                        
                    # Prioritize realtime sheet for specific columns for overall context
                    summary_row_dict['功能模块'] = rt_row.get('功能模块')
                    summary_row_dict['测试用例'] = rt_row.get('测试用例')
                    summary_row_dict['turns'] = rt_row.get('turns') # This indicates it's a TTS turn
                    
                    # Ensure original ID from rt_row is used for the merged row.
                    if 'ID' in rt_row:
                        summary_row_dict['ID'] = rt_row['ID'] 
                    elif 'ID' in tts_row_to_merge: # Fallback, if rt_row somehow misses ID
                        summary_row_dict['ID'] = tts_row_to_merge['ID']

                    summary_data.append(summary_row_dict)
                    
                    # Increment counter for the next TTS turn of this ID
                    tts_turn_counters[case_id_str] = tts_idx_for_id + 1
                    merged_for_this_rt_row = True
                else:
                    # TTS turn in realtime, but no (more) matching ID or variant in tts sheet. Keep original rt_row.
                    if matching_tts_rows_for_id.empty:
                        print(f"  ID '{case_id_str}' is a TTS turn in realtime (row {index+2}), but no matching ID found in tts sheet. Keeping realtime row.")
                    else: # tts_idx_for_id >= len(matching_tts_rows_for_id)
                        print(f"  ID '{case_id_str}' is a TTS turn in realtime (row {index+2}), but all {len(matching_tts_rows_for_id)} TTS variants for this ID have been used. Keeping realtime row.")

            # If the rt_row was not merged with voice or TTS data, append it as is
            if not merged_for_this_rt_row:
                summary_data.append(rt_row.drop('ID_str', errors='ignore').to_dict())
        
        if not summary_data:
            print("No data to write to summary sheet.")
            return

        summary_df = pd.DataFrame(summary_data)

        # Define final column order based on realtime_df, then add any new columns from voice_df
        final_columns = [col for col in realtime_df.columns if col != 'ID_str']
        for col in summary_df.columns:
            if col not in final_columns:
                final_columns.append(col)
        summary_df = summary_df.reindex(columns=final_columns)

        # Convert 'ID' column to int type
        summary_df['ID'] = summary_df['ID'].astype(int)
        
        # Write the summary_df to a new sheet named 'summary' in the same Excel file
        # Using openpyxl to add a new sheet without disturbing others and to handle existing files better.
        try:
            book = load_workbook(excel_file_path)
            # Remove existing summary sheet if it exists
            if 'summary' in book.sheetnames:
                del book['summary']
                print("Removed existing 'summary' sheet.")
            
            # Create new summary sheet
            summary_sheet = book.create_sheet('summary')
            
            # Define a style for the header
            header_font = Font(bold=True, color="FFFFFF") # White text
            # Example: A dark blue fill. You can pick any RGB color.
            header_fill = PatternFill(start_color="FF00008B", end_color="FF00008B", fill_type="solid") # Dark Blue
            header_alignment = Alignment(horizontal="center", vertical="center")
            thin_border = Border(left=Side(style='thin'), 
                                 right=Side(style='thin'), 
                                 top=Side(style='thin'), 
                                 bottom=Side(style='thin'))

            # Write dataframe to sheet
            for r_idx, row in enumerate(dataframe_to_rows(summary_df, index=False, header=True)):
                for c_idx, value in enumerate(row):
                    cell = summary_sheet.cell(row=r_idx + 1, column=c_idx + 1, value=value)
                    if r_idx == 0: # This is the header row
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment
                        cell.border = thin_border
                    else: # Data rows (optional: apply a different style or just borders)
                        cell.border = thin_border 
            
            # Auto-adjust column widths (optional, can be slow for very large sheets)
            MAX_COLUMN_WIDTH = 50  # Define a maximum width for any column
            # Define specific max widths for certain columns if needed
            SPECIFIC_MAX_WIDTHS = {
                'messages': 80, # Allow 'messages' to be a bit wider
                'output_transcript': 70,
                'transcript': 70,
                # Add other column names and their specific max widths here
            }

            for column_cells in summary_sheet.columns:
                # Calculate the maximum length of content in the column
                # Handle potential None values by converting to empty string for len()
                try:
                    base_width = max(len(str(cell.value)) if cell.value is not None else 0 for cell in column_cells)
                except TypeError: # Handle cases where cell.value might be complex type not directly convertible to str for len
                    base_width = 20 # Default if type error occurs
                
                column_letter = column_cells[0].column_letter
                column_title = summary_sheet[column_letter + '1'].value # Get header title

                # Determine the max width for this specific column
                current_max_width = SPECIFIC_MAX_WIDTHS.get(column_title, MAX_COLUMN_WIDTH)
                
                # Set the width: min(calculated_length + padding, max_width_for_this_column)
                # Ensure header text itself is also considered for width calculation
                header_length = len(str(column_title)) if column_title is not None else 0
                final_width = max(base_width, header_length) + 2 # Add padding
                final_width = min(final_width, current_max_width)
                
                summary_sheet.column_dimensions[column_letter].width = final_width

            book.save(excel_file_path)
            print(f"Successfully merged results into 'summary' sheet in {excel_file_path}")

            # Create performance sheet after summary sheet is created
            create_performance_sheet(excel_file_path)

        except Exception as e_write:
            print(f"Error writing 'summary' sheet to {excel_file_path}: {e_write}")

    except FileNotFoundError:
        print(f"Error: Excel file not found at {excel_file_path}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def create_performance_sheet(excel_file_path: str):
    """
    Creates a 'performance' sheet with statistics from the 'summary' sheet.

    Calculates average response times for:
    - realtime首字响应时间
    - tts首字响应时间
    - voice首字响应时间
    """
    try:
        print(f"Creating performance sheet in {excel_file_path}")

        # Load the Excel file
        xls = pd.ExcelFile(excel_file_path, engine='openpyxl')
        if 'summary' not in xls.sheet_names:
            print(f"Error: Sheet 'summary' not found in {excel_file_path}")
            return

        # Read summary sheet
        summary_df = pd.read_excel(xls, sheet_name='summary', engine='openpyxl')
        print(f"Loaded 'summary' sheet with {len(summary_df)} rows.")

        # Define the time columns to analyze
        time_columns = {
            'realtime首字响应时间': 'realtime首字响应平均时间',
            'tts首字响应时间': 'tts首字响应平均时间',
            'voice首字响应时间': 'voice首字响应平均时间'
        }

        # Calculate statistics
        performance_data = []

        for time_col, avg_col in time_columns.items():
            if time_col in summary_df.columns:
                # Filter valid time data (non-empty, non-zero, numeric)
                valid_times = []
                for value in summary_df[time_col]:
                    if pd.notna(value) and str(value).strip() != '':
                        try:
                            # Convert to float and filter out zero values
                            time_val = float(value)
                            if time_val > 0:
                                valid_times.append(time_val)
                        except (ValueError, TypeError):
                            continue

                # Calculate average
                if valid_times:
                    avg_time = np.mean(valid_times)
                    count = len(valid_times)
                    min_time = min(valid_times)
                    max_time = max(valid_times)

                    performance_data.append({
                        '指标': avg_col,
                        '平均值': f"{avg_time:.0f}",
                        '样本数量': count,
                        '最小值': f"{min_time:.0f}",
                        '最大值': f"{max_time:.0f}",
                        '单位': '毫秒'
                    })
                    print(f"  {time_col}: 平均 {avg_time:.0f}ms (样本数: {count})")
                else:
                    performance_data.append({
                        '指标': avg_col,
                        '平均值': '无数据',
                        '样本数量': 0,
                        '最小值': '无数据',
                        '最大值': '无数据',
                        '单位': '毫秒'
                    })
                    print(f"  {time_col}: 无有效数据")
            else:
                print(f"  Warning: Column '{time_col}' not found in summary sheet")
                performance_data.append({
                    '指标': avg_col,
                    '平均值': '列不存在',
                    '样本数量': 0,
                    '最小值': '列不存在',
                    '最大值': '列不存在',
                    '单位': '毫秒'
                })

        if not performance_data:
            print("No performance data to write.")
            return

        # Create performance DataFrame
        performance_df = pd.DataFrame(performance_data)

        # Write to Excel
        book = load_workbook(excel_file_path)

        # Remove existing performance sheet if it exists
        if 'performance' in book.sheetnames:
            del book['performance']
            print("Removed existing 'performance' sheet.")

        # Create new performance sheet
        performance_sheet = book.create_sheet('performance')

        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="FF006400", end_color="FF006400", fill_type="solid")  # Dark Green
        header_alignment = Alignment(horizontal="center", vertical="center")
        thin_border = Border(left=Side(style='thin'),
                            right=Side(style='thin'),
                            top=Side(style='thin'),
                            bottom=Side(style='thin'))

        # Write dataframe to sheet
        for r_idx, row in enumerate(dataframe_to_rows(performance_df, index=False, header=True)):
            for c_idx, value in enumerate(row):
                cell = performance_sheet.cell(row=r_idx + 1, column=c_idx + 1, value=value)
                if r_idx == 0:  # Header row
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = thin_border
                else:  # Data rows
                    cell.border = thin_border
                    # Center align numeric data
                    if c_idx in [1, 2, 3, 4]:  # 平均值, 样本数量, 最小值, 最大值 columns
                        cell.alignment = Alignment(horizontal="center", vertical="center")

        # Auto-adjust column widths
        for column_cells in performance_sheet.columns:
            try:
                base_width = max(len(str(cell.value)) if cell.value is not None else 0 for cell in column_cells)
            except TypeError:
                base_width = 20

            column_letter = column_cells[0].column_letter
            column_title = performance_sheet[column_letter + '1'].value
            header_length = len(str(column_title)) if column_title is not None else 0
            final_width = max(base_width, header_length) + 2
            final_width = min(final_width, 30)  # Max width of 30

            performance_sheet.column_dimensions[column_letter].width = final_width

        book.save(excel_file_path)
        print(f"Successfully created 'performance' sheet in {excel_file_path}")

    except Exception as e:
        print(f"Error creating performance sheet: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Merge 'realtime' and 'voice' sheet results into a 'summary' sheet in an Excel report.")
    parser.add_argument("excel_file", type=str, help="Path to the Excel report file.")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.excel_file):
        print(f"Error: Input file does not exist: {args.excel_file}")
    elif not args.excel_file.lower().endswith(('.xlsx', '.xls')):
        print(f"Error: Input file must be an Excel file (.xlsx or .xls): {args.excel_file}")
    else:
        merge_sheets(args.excel_file)
