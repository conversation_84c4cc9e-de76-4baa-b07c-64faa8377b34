#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
from typing import List


def split_text_with_punctuation(text):
    """
    使用用户提供的切句方法
    定义切句符号（按长度从长到短排序）
    """
    punctuations = [
        r'\$-\$', r'\|\n', r'~', r'～', r'\n', r'：', r'>', r'、',
        r'"', r'"', r'！', r'？', r'，', r'。', r'!', r'\?', r':'
    ]

    # 构建正则表达式：用 lookahead 保留标点符号在当前句末
    pattern = f"(.*?(?:{'|'.join(punctuations)}))"

    # 使用 re.findall 获取所有匹配片段
    matches = re.findall(pattern, text)

    # 找出剩余内容（如果最后没有标点，也包含进去）
    remainder = re.sub(pattern, '', text)
    if remainder.strip():
        matches.append(remainder)

    return [s for s in matches if s.strip()]


class TextSplitter:
    """文本切句工具类"""

    # 定义切句符号
    SPLIT_SYMBOLS = [
        '。"',
        '！"',
        '？"',
        '。',
        '，',
        '！',
        '？',
        '!',
        '：',
        ':"',
        '、',
        '>',
        '|\n',
        '\n',
        '～',
        '~',
        '：',
        '$-$'
    ]
    
    @classmethod
    def split_text(cls, text: str, keep_punctuation: bool = True) -> List[str]:
        """
        将文本按照指定的符号进行切句

        Args:
            text: 要切分的文本
            keep_punctuation: 是否保留标点符号（当前总是保留标点）

        Returns:
            切分后的文本片段列表
        """
        if not text or not text.strip():
            return []

        # 使用用户提供的切句方法
        return split_text_with_punctuation(text)
    
    @classmethod
    def split_text_simple(cls, text: str) -> List[str]:
        """
        简单的文本切分，不保留标点符号
        
        Args:
            text: 要切分的文本
            
        Returns:
            切分后的文本片段列表（不包含标点符号）
        """
        return cls.split_text(text, keep_punctuation=False)
    
    @classmethod
    def get_split_symbols(cls) -> List[str]:
        """
        获取所有切句符号
        
        Returns:
            切句符号列表
        """
        return cls.SPLIT_SYMBOLS.copy()
    
    @classmethod
    def add_split_symbol(cls, symbol: str):
        """
        添加新的切句符号
        
        Args:
            symbol: 要添加的符号
        """
        if symbol not in cls.SPLIT_SYMBOLS:
            cls.SPLIT_SYMBOLS.append(symbol)
    
    @classmethod
    def remove_split_symbol(cls, symbol: str):
        """
        移除切句符号
        
        Args:
            symbol: 要移除的符号
        """
        if symbol in cls.SPLIT_SYMBOLS:
            cls.SPLIT_SYMBOLS.remove(symbol)


def test_text_splitter():
    """测试文本切句功能"""
    test_cases = [
        "你好，我是银河精灵。今天天气怎么样？",
        "这是一个测试！\"很好的测试。",
        "第一句话。\n第二句话，继续说话：这样就行了～",
        "简单测试$-$分隔符测试>结束",
        "没有标点的文本",
        "",
        "   ",
        "只有标点。",
    ]
    
    print("=" * 50)
    print("文本切句工具测试")
    print("=" * 50)
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: '{test_text}'")
        
        # 测试保留标点
        result_with_punct = TextSplitter.split_text(test_text, keep_punctuation=True)
        print(f"保留标点: {result_with_punct}")
        
        # 测试不保留标点
        result_without_punct = TextSplitter.split_text(test_text, keep_punctuation=False)
        print(f"不保留标点: {result_without_punct}")
    
    print("\n" + "=" * 50)
    print(f"支持的切句符号: {TextSplitter.get_split_symbols()}")
    print("=" * 50)


if __name__ == "__main__":
    test_text_splitter()
