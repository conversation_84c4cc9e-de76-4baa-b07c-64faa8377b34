import sys
import os
import re
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.websocket_client import WebSocketClient
import json
import base64

class TTSEventHandler:
    def __init__(self, base_url, headers=None):
        self.base_url = base_url  # 保存API地址
        self.client = WebSocketClient(base_url, headers)

        self.session_id = None
        self.event_id = None

    async def connect(self):
        print(f"\n[DEBUG] 连接到TTS API: {self.base_url}")
        await self.client.connect()
        connection_response = await self.client.receive()
        print(f"[DEBUG] TTS连接响应: {connection_response}")
        if connection_response.get('type') == 'tts.connection.done':
            self.session_id = str(connection_response['data']['session_id'])
            print(f"[DEBUG] TTS会话创建成功，session_id: {self.session_id}")
        else:
            raise ConnectionError("WebSocket连接失败")

    async def create_session(self, voice_id, response_format='mp3_stream', speed_ratio=1.0, volume_ratio=1.0, sample_rate=24000,
                             tts_mode="sentence", emotion=None, language=None):
        # 记录当前tts_mode
        if tts_mode == 'slice':
            self.tts_mode = 'default'
        else:
            self.tts_mode = 'sentence'
        # 准备payload数据
        data = {
            "session_id": self.session_id,
            "role_id": voice_id,
            "response_format": response_format,
            "speed_ratio": speed_ratio,
            "volume_ratio": volume_ratio,
            "sample_rate": sample_rate,
            "mode": self.tts_mode
        }

        # 处理voice_label参数（情绪和语言/方言）
        voice_label = {}
        if emotion and language:
            print(f"[DEBUG] 警告: tts接口不支持情绪和语言和方言参数同时存在: {emotion}, {language}")
        if emotion:
            voice_label["emotion"] = emotion
        if language:
            voice_label["language"] = language
            
        # 如果有voice_label参数，添加到payload中
        if voice_label:
            data["voice_label"] = voice_label
            print(f"[DEBUG] 添加voice_label参数: {data['voice_label']}")

        payload = {
            "type": "tts.create",
            "data": data
        }
        print(f"\n[DEBUG] 发送给TTS API: {self.base_url}")
        print(f"[DEBUG] 创建TTS会话，voice_id: {voice_id}, speed_ratio: {speed_ratio}, volume_ratio: {volume_ratio}, emotion: {emotion if emotion else '无'}, language: {language if language else '无'}")
        print(f"[DEBUG] 完整的payload: {json.dumps(payload, ensure_ascii=False)}")
        await self.client.send(payload)
        response = await self.client.receive()
        print(f"[DEBUG] TTS创建会话响应: {response}")
        if response.get('type') == 'tts.response.error':
            raise ValueError(f"创建会话失败: {response['data']['message']}")
        return response

    async def send_text(self, text):
        # 根据tts_mode决定发送方式
        if getattr(self, 'tts_mode', 'sentence') == 'default':
            # 按标点切片并保留标点
            parts = re.split(r'([，,。！？])', text)
            slices = []
            tmp = ''
            for part in parts:
                if part in '，,。！？':
                    tmp += part
                    slices.append(tmp.strip())
                    tmp = ''
                else:
                    tmp += part
            if tmp.strip():  # 处理最后无标点结尾的部分
                slices.append(tmp.strip())
            for idx, piece in enumerate(slices):
                text_payload = {
                    "type": "tts.text.delta",
                    "data": {
                        "session_id": self.session_id,
                        "text": piece
                    }
                }
                print(f"\n[DEBUG] 发送给TTS API: {self.base_url}")
                print(f"[DEBUG] 发送文本切片({idx+1}/{len(slices)}): {piece[:50]}{'...' if len(piece) > 50 else ''}")
                await self.client.send(text_payload)
        else:
            # sentence模式，原有逻辑
            text_payload = {
                "type": "tts.text.delta",
                "data": {
                    "session_id": self.session_id,
                    "text": text
                }
            }
            print(f"\n[DEBUG] 发送给TTS API: {self.base_url}")
            print(f"[DEBUG] 发送文本内容: {text[:50]}{'...' if len(text) > 50 else ''}")
            await self.client.send(text_payload)

        done_payload = {
            "type": "tts.text.done",
            "data": {"session_id": self.session_id}
        }
        print("[DEBUG] 发送文本完成信号")
        await self.client.send(done_payload)

    async def receive_audio(self, callback):
        print(f"\n[DEBUG] 等待来自TTS API: {self.base_url} 的音频响应")
        start_time = asyncio.get_event_loop().time()
        first_audio_time = None

        while True:
            response = await self.client.receive()
            print(f"[DEBUG] 收到TTS响应类型: {response['type']}")

            # 检查是否是流式音频响应
            if response['type'] == 'tts.response.audio.delta':
                # 记录首字响应时间（第一次收到音频数据时）
                if first_audio_time is None:
                    current_time = asyncio.get_event_loop().time()
                    first_audio_time = current_time - start_time
                    first_audio_time_ms = first_audio_time * 1000
                    print(f"TTS首字响应时间: {first_audio_time_ms:.0f}")
            elif response['type'] == 'tts.response.audio.done':
                self.event_id = str(response['event_id'])
                print(f"[DEBUG] TTS音频生成完成，event_id: {self.event_id}")
                audio_data = base64.b64decode(response['data']['audio'])
                print(f"[DEBUG] 收到音频数据长度: {len(audio_data)} 字节")
                callback(audio_data)
                break

        return first_audio_time

    async def close(self):
        await self.client.close()
