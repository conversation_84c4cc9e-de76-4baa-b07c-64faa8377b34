import sys
import os
import json
import pandas as pd
import copy

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.websocket_client import WebSocketClient


class RealtimeEventHandler:
    """实时对话事件处理器"""

    def __init__(self, base_url, headers, username=None):
        """初始化实时对话事件处理器

        Args:
            base_url: WebSocket服务器地址
            headers: 请求头
            username: 用户名
        """
        self.base_url = base_url
        self.headers = headers
        self.username = username
        self.ws = None
        self.session_url = None
        self.client = WebSocketClient(base_url, headers)
        print(f"[DEBUG] 初始化RealtimeEventHandler")

    async def connect(self):
        await self.client.connect(self.headers)

    async def append_audio_buffer(self, audio: str, turn_id: str):
        payload = {
            "event_id": f"event_{os.urandom(4).hex()}",
            "turn_id": turn_id,
            "type": "input_audio_buffer.append",
            "audio": audio
        }
        print(f"\n[DEBUG] 发送给API: {self.base_url}")
        print("[DEBUG] 发送append_audio_buffer请求")
        await self.client.send(payload)

    async def commit_audio_buffer(self, turn_id: str, transcript: str, language: str = "中文"):
        payload = {
            "event_id": f"event_{os.urandom(4).hex()}",
            "turn_id": turn_id,
            "type": "input_audio_buffer.commit",
            "transcript": transcript,
            "language": language
        }
        print(f"\n[DEBUG] 发送给API: {self.base_url}")
        print("[DEBUG] 发送commit_audio_buffer请求")
        print(f"[DEBUG] payload: {payload}")
        # print(f"[DEBUG] transcript: {transcript[:50]}{'...' if len(transcript) > 50 else ''}")

        await self.client.send(payload)
        # response = await self.client.receive()
        # if response.get('type') == 'input_audio_buffer.error':
        #     raise ValueError(f"音频提交失败: {response['data']['message']}")

    async def create_response(self, role_id: str, pattern: str = "normal", role: str = "user", text: str = ""):
        payload = {
            "event_id": f"event_{os.urandom(4).hex()}",
            "type": "response.create",
            "response": {
                "role_id": role_id,
                "pattern": pattern,
                "messages": [{
                    "role": role,
                    "type": "text",
                    "text": text
                }]
            }
        }
        print(f"\n[DEBUG] 发送给API: {self.base_url}")
        print("[DEBUG] 发送create_response请求")
        print(f"[DEBUG] role_id: {role_id}, pattern: {pattern}")
        print(f"[DEBUG] text: {text[:50]}{'...' if len(text) > 50 else ''}")
        await self.client.send(payload)

    async def create_multi_response(self, role_id: str, pattern: str = "normal", conversation_history: list = [], speed_ratio: float = None, volume_ratio: float = None):
        # 调试输出：打印对话历史
        print("\n[DEBUG] create_multi_response - 对话历史:")
        print(json.dumps(conversation_history, ensure_ascii=False, indent=2))

        # 检查对话历史中是否有nan值
        for i, msg in enumerate(conversation_history):
            if 'text' in msg and pd.isna(msg['text']):
                print(f"\n[DEBUG] 警告：对话历史中第{i+1}条消息的text是NaN值，将替换为空字符串")
                msg['text'] = ""

        # 准备response部分
        response_data = {
            "role_id": role_id,
            "pattern": pattern,
            "messages": conversation_history
        }
        
        # 如果提供了速度比率，添加到response中
        if speed_ratio and not pd.isna(speed_ratio):
            try:
                speed_ratio_float = round(float(speed_ratio), 1)
                response_data["speed_ratio"] = speed_ratio_float
                print(f"[DEBUG] 添加语速参数: {speed_ratio_float}")
            except (ValueError, TypeError):
                print(f"[DEBUG] 警告: 语速参数格式不正确: {speed_ratio}")
                
        # 如果提供了音量比率，添加到response中
        if volume_ratio and not pd.isna(volume_ratio):
            try:
                volume_ratio_float = round(float(volume_ratio), 1)
                response_data["volume_ratio"] = volume_ratio_float  
                print(f"[DEBUG] 添加音量参数: {volume_ratio_float}")
            except (ValueError, TypeError):
                print(f"[DEBUG] 警告: 音量参数格式不正确: {volume_ratio}")

        payload = {
            "event_id": f"event_{os.urandom(4).hex()}",
            "type": "response.create",
            "response": response_data
        }

        print(f"\n[DEBUG] 发送给API: {self.base_url}")
        print("[DEBUG] 发送的payload:")
        # 创建一个副本进行打印，避免打印过长的内容
        # payload_copy = copy.deepcopy(payload)
        # if 'response' in payload_copy and 'messages' in payload_copy['response']:
        #     for msg in payload_copy['response']['messages']:
        #         if 'text' in msg and isinstance(msg['text'], str) and len(msg['text']) > 100:
        #             msg['text'] = msg['text'][:100] + '... (已省略)'
        # print(json.dumps(payload_copy, ensure_ascii=False, indent=2))
        print(json.dumps(payload, ensure_ascii=False, indent=2))
        await self.client.send(payload)

    async def create_voice(self, base_voice_id: str, style: str, text_style: str, messages: str):
        payload = {
            "event_id": f"event_{os.urandom(4).hex()}",
            "type": "voice.create",
            "response": {
                "voice_id": base_voice_id,
                "style": style,
                "text_style": text_style,
                "messages": [{
                    "role": "user",
                    "type": "text",
                    "text": messages
                }]
            }
        }
        await self.client.send(payload)

    async def receive(self):
        response = await self.client.receive()

        # 打印API地址和简化的响应信息，避免过长的内容
        print(f"\n[DEBUG] 接收来自API: {self.base_url} 的响应")
        if response:
            response_copy = copy.deepcopy(response) if isinstance(response, dict) else response
            if isinstance(response_copy, dict):
                # 如果有delta字段且内容过长，简化输出
                if 'delta' in response_copy and isinstance(response_copy['delta'], str) and len(response_copy['delta']) > 50:
                    response_copy['delta'] = response_copy['delta'][:50] + '... (已省略)'
                # 如果有audio字段，简化输出
                if 'audio' in response_copy:
                    response_copy['audio'] = '<音频数据已省略>'
            print(response_copy)
        else:
            print(response)

        if response is None:
            raise ConnectionError("WebSocket连接已关闭，接收到的响应为空")
        return response

    async def close(self):
        if self.client.websocket is not None:  # 检查 websocket 是否存在
            await self.client.close()
            self.client.websocket = None  # 关闭后置空，避免重复关闭
        else:
            print("Warning: WebSocket connection was not established or already closed.")
