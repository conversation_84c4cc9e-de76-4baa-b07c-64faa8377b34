import asyncio
import base64
import os
from datetime import datetime
import json
import pandas as pd
import time
import uuid
from typing import Dict, List, Optional, Tuple
from utils.excel_writer import ExcelWriter
from utils.audio_file_handler import AudioFileHandler
from utils.config_manager import get_config_manager
from utils.progress_display import ProgressDisplay
from utils.test_status import TestStatus
from utils.session_manager import SessionManager
from workflow.tts_workflow import TTSWorkflow
from workflow.chat_workflow import ChatWorkflow
from workflow.voice_workflow import VoiceCreateWorkflow
from workflow.role_api import RoleAPI
from utils.data_handler import clean_string

# 初始化配置管理器
config_manager = get_config_manager()

class BaseRealtimeWorkflow:
    """基础实时对话工作流类"""
    
    def __init__(self, max_history_turns: int = 5, no_audio_transcript: bool = False):
        self.writer = ExcelWriter()
        self.max_history_turns = max_history_turns
        self.config_manager = get_config_manager()
        self.no_audio_transcript = no_audio_transcript

    async def execute_conversation_turn(
        self,
        handler,
        case: Dict,
        conversation_history: List[Dict],
        index: int,
        row_index: int,
        excel_file: str,
        max_retries: int
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """执行单轮对话. 返回 (success, assistant_text, error_message)"""
        retry_count = 0
        last_error = None
        while retry_count < max_retries:
            start_time = datetime.now()
            try:
                print(f"\n开始执行第 {index + 1} 轮对话 (重试次数: {retry_count + 1}/{max_retries})")
                print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")

                # 先生成 session-id 并设置到 headers
                new_session_id = self._generate_session_id(handler)
                handler.headers["Session-ID"] = new_session_id
                print(f"设置 session-id: {new_session_id}")

                # 然后重置连接
                await self._reset_connection(handler)

                request_messages = await self._send_conversation_history(handler, case, conversation_history, index)
                transcript, audio_data, response, first_word_time = await self._receive_response(handler, timeout=300)

                await self._save_realtime_results(
                    test_case=case,
                    audio_data=audio_data,
                    transcript=transcript,
                    response=response,
                    excel_file=excel_file,
                    row_index=row_index,
                    request_messages=request_messages,
                    session_id=new_session_id,
                    first_word_time=first_word_time
                )

                return True, transcript, None # 成功时返回 transcript, 无错误信息

            except Exception as e:
                error_msg = f"多轮对话执行失败 (尝试 {retry_count + 1}/{max_retries}): {str(e)}"
                print(error_msg)
                last_error = error_msg # 记录最后一次错误
                retry_count += 1
                if retry_count >= max_retries:
                    # 所有重试失败后返回
                    return False, None, last_error
                await asyncio.sleep(5)

        # 如果循环因为某种原因退出（理论上不应该没有返回值）
        return False, None, last_error or "未知错误导致对话执行退出"

    def _generate_session_id(self, handler) -> str:
        """生成会话ID"""
        username = getattr(handler, 'username', None)
        return SessionManager.create_session_id(username)

    async def _reset_connection(self, handler):
        """重置WebSocket连接"""
        try:
            await handler.close()
        except Exception:
            pass
        await asyncio.sleep(2)
        await handler.connect()

    async def _send_conversation_history(self, handler, case: Dict, conversation_history: List[Dict], index: int) -> List[Dict]:
        """发送对话历史"""
        self._debug_print_case(case)

        await handler.append_audio_buffer(
            audio=case['sample_audio'],
            turn_id=case['turn_id']
        )
        transcript = '' if self.no_audio_transcript else case['transcript']
        language = '' if self.no_audio_transcript else case['language']
        await handler.commit_audio_buffer(
            turn_id=case['turn_id'],
            transcript=transcript,
            language=language
        )

        current_history = self._prepare_conversation_history(case, conversation_history, index)

        # 获取语速和音量参数（如果存在）
        speed_ratio = case.get('语速', None)
        volume_ratio = case.get('音量', None)
        
        # 传递给create_multi_response方法
        await handler.create_multi_response(
            role_id=case['role_id'],
            pattern=case['pattern'],
            conversation_history=current_history,
            speed_ratio=speed_ratio,
            volume_ratio=volume_ratio
        )

        return current_history

    def _debug_print_case(self, case: Dict):
        """调试输出case内容"""
        print("\n[DEBUG] _send_conversation_history - case内容:")
        for k, v in case.items():
            if k != 'sample_audio':
                print(f"  {k}: {v}")
            else:
                print(f"  {k}: <音频数据已省略>")

    def _prepare_conversation_history(self, case: Dict, conversation_history: List[Dict], index: int) -> List[Dict]:
        history_to_process = conversation_history.copy() # Work on a copy

        # Apply history limit only to actual conversation turns
        max_history_len = self.max_history_turns * 2

        # Truncate history if necessary
        if len(history_to_process) > max_history_len:
            print(f"\n[DEBUG] 历史对话({len(history_to_process)}) 超过{self.max_history_turns}轮({max_history_len})，只保留最近{self.max_history_turns}轮对话")
            history_to_process = history_to_process[-max_history_len:]

        # Use the (potentially truncated) history directly
        final_history = history_to_process

        return final_history

    async def _receive_response(self, handler, timeout: int = 300) -> Tuple[str, bytes, Dict, Optional[float]]:
        """接收响应，返回 (assistant_text, audio_chunks, response, first_word_time)"""
        audio_chunks = b""
        transcript_chunks = []
        assistant_text = None
        audio_complete = False
        transcript_complete = False
        first_word_time = None  # 首字回复时间

        start_time = asyncio.get_event_loop().time()

        while True:
            try:
                current_time = asyncio.get_event_loop().time()
                elapsed = current_time - start_time
                remaining = timeout - elapsed

                if remaining <= 0:
                    raise asyncio.TimeoutError(f"接收响应已等待 {elapsed:.2f}秒，超过设定的 {timeout}秒")

                print(f"正在等待响应，已等待: {elapsed:.2f}秒，剩余: {remaining:.2f}秒")
                response = await asyncio.wait_for(handler.receive(), timeout=remaining)

                receive_time = asyncio.get_event_loop().time()
                response_time = receive_time - current_time
                total_elapsed = receive_time - start_time
                print(f"收到响应，耗时: {response_time:.2f}秒，总计等待: {total_elapsed:.2f}秒")

                response_type = response.get('type')
                if response_type == 'conversation.created':
                    continue
                elif response_type == 'response.created':
                    continue
                elif response_type == 'response.audio.delta':
                    audio_chunks += base64.b64decode(response['delta'])
                elif response_type == 'response.audio_transcript.delta':
                    delta_content = response['delta']
                    if delta_content != '\x00': # 检查 delta 是否为 '\x00'
                        transcript_chunks.append(delta_content)
                        # 记录首字回复时间（第一次收到有效转录文本时）
                        if first_word_time is None and delta_content.strip():
                            first_word_time = total_elapsed
                            first_word_time_ms = first_word_time * 1000
                            print(f"首字回复时间: {first_word_time_ms:.0f} ms")
                elif response_type == 'response.audio.done':
                    audio_complete = True
                elif response_type == 'response.audio_transcript.done':
                    transcript_complete = True
                    assistant_text = ''.join(transcript_chunks)
                    print(f'完整转录文本(用时 {total_elapsed:.2f}秒): {assistant_text}')
                elif response_type == 'response.done':
                    if not audio_complete or not transcript_complete:
                        raise ValueError("未收到完整的音频或转录数据")
                    final_time = asyncio.get_event_loop().time()
                    total_time = final_time - start_time
                    print(f"响应接收完成，总计用时: {total_time:.2f}秒")
                    return assistant_text, audio_chunks, response, first_word_time
                else:
                    print(f"未知响应类型: {response_type}")
            except asyncio.TimeoutError as e:
                timeout_time = asyncio.get_event_loop().time()
                timeout_elapsed = timeout_time - start_time
                print(f"超时发生，已等待时间: {timeout_elapsed:.2f}秒，超时设置: {timeout}秒")
                raise

    async def _save_realtime_results(
        self,
        test_case: Dict,
        audio_data: bytes,
        transcript: str,
        response: Dict,
        excel_file: str,
        row_index: int,
        request_messages: List[Dict],
        session_id: str,
        first_word_time: Optional[float] = None,
        tts_first_word_time: Optional[float] = None
    ):
        """保存测试结果"""
        
        audio_path = None
        # 检查是否是直接TTS用例
        is_direct_tts = isinstance(test_case.get('turns'), str) and test_case['turns'].lower().startswith('tts')

        if is_direct_tts:
            # 直接使用TTS生成的音频路径
            audio_path = test_case.get('audio_path', '')
            session_url = SessionManager.create_tts_session_url(session_id)
            print(f"[DEBUG] 使用TTS生成的音频文件路径: {audio_path}")
        elif audio_data: # 确保有 audio_data 数据才保存
            # 对于实时对话，保存接收到的音频数据 (仍保存为mp3)
            print("[DEBUG] 保存实时对话的音频文件(输出格式: mp3)...")
            audio_path = AudioFileHandler.save_audio_file(
                audio_data=audio_data,
                test_case=test_case,
                test_type='realtime', # 或者根据需要区分
                turn_id=test_case['turn_id'],
                audio_output_dir=self.audio_output_dir
            )
            print(f"[DEBUG] 实时对话音频已保存到: {audio_path}")
            session_url = SessionManager.create_session_url(session_id)
        else:
            print("[DEBUG] 警告：无法保存音频。")
            audio_path = '' # 设为空字符串或其他标记
            session_url = SessionManager.create_session_url(session_id)

        data = {
            'output_transcript': transcript,
            'audio_mp3': audio_path,
            'status': str(TestStatus.SUCCESS),
            'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'event_id': response.get('event_id', '')
        }

        # 添加首字回复时间
        if first_word_time is not None:
            first_word_time_ms = first_word_time * 1000
            data['realtime首字响应时间'] = f"{first_word_time_ms:.0f}"
            print(f"[DEBUG] 记录首字回复时间: {first_word_time_ms:.0f}")
        else:
            data['realtime首字响应时间'] = ''

        # 添加TTS首字响应时间
        if tts_first_word_time is not None:
            tts_first_word_time_ms = tts_first_word_time * 1000
            data['tts首字响应时间'] = f"{tts_first_word_time_ms:.0f}"
            print(f"[DEBUG] 记录TTS首字响应时间: {tts_first_word_time_ms:.0f}")
        else:
            data['tts首字响应时间'] = ''

        if 'transcript' in test_case:
            data['transcript'] = test_case['transcript']

        print(f"[DEBUG] 添加session URL到结果数据: {session_url}")
        data['session'] = session_url

        if request_messages:
            data['messages'] = json.dumps(request_messages, ensure_ascii=False, indent=2)

        self.writer.write_to_excel(
            excel_path=excel_file,
            sheet_name='realtime',
            data_dict={row_index: data}
        )

class ProgressRealtimeWorkflow(BaseRealtimeWorkflow):
    """带进度显示的实时对话工作流"""

    def __init__(self, max_history_turns: Optional[int] = None, no_audio_transcript: bool = False, tts_mode: str = 'sentence'):
        """初始化带进度显示的多轮对话工作流"""
        max_history_turns = max_history_turns or self.config_manager.get('max_history_turns', 5)
        super().__init__(max_history_turns=max_history_turns, no_audio_transcript=no_audio_transcript)
        self.tts_mode = tts_mode
        self.tts_workflow = TTSWorkflow(concurrency_limit=self.config_manager.get('concurrency_limit', 1), tts_mode=tts_mode)
        self.chat_workflow = ChatWorkflow(
            model=self.config_manager.get('aigc_mode.model'),
            api_key=self.config_manager.get('aigc_mode.api_key'),
            base_url=self.config_manager.get('aigc_mode.base_url'),
            prompt_config='config/prompt_config.yaml'
        )

    async def execute_multi_realtime_sequence(
        self,
        handler,
        grouped_cases: Dict,
        excel_file: str,
        audio_output_dir: str,
        max_retries: int,
        start_id: Optional[str] = None,
        voice_cases: Optional[Dict] = None,
        audio_format: str = 'pcm',
        no_audio_transcript: bool = False,
        tts_mode: str = 'sentence'
    ) -> Dict[str, int]:
        """执行多轮对话测试序列，带进度显示. 返回包含统计结果的字典。"""
        self.no_audio_transcript = no_audio_transcript
        self.tts_mode = tts_mode
        self.tts_workflow.tts_mode = tts_mode
        try:
            df = pd.read_excel(excel_file, sheet_name='realtime')
            df = df.fillna('') # 将所有NaN值转换为空字符串
            tts_cases = self._load_tts_cases(excel_file)
            
            # 保存音频输出目录供后续使用
            self.audio_output_dir = audio_output_dir

            total_cases = len(grouped_cases)
            total_turns = sum(len(cases) for cases in grouped_cases.values())
            completed_cases = 0
            completed_turns = 0
            skipped_cases = 0

            progress_display = ProgressDisplay()
            progress_display.start_progress(total_turns, description="执行测试用例...")
            start_time = time.time()
            results = []
            start_id_found = False if start_id else True

            for case_id, cases in grouped_cases.items():
                if not self._should_process_case(case_id, start_id, start_id_found):
                    skipped_cases += 1
                    completed_turns += len(cases)
                    progress_display.update_progress(len(cases))
                    continue

                if start_id and str(case_id) == start_id:
                    start_id_found = True
                    print(f"\n已找到起始ID {start_id}，开始执行测试")

                case_rows = df[df['ID'] == case_id]
                case_indices = case_rows.index.tolist()

                # 获取第一行的测试用例和transcript
                first_row = case_rows.iloc[0]
                test_scenario = ''
                first_transcript = ''
                    
                # 检查'测试用例'列是否存在，不存在则使用空字符串
                if '测试用例' in first_row:
                    test_scenario = first_row['测试用例']
                    if pd.isna(test_scenario):
                        test_scenario = ''
                    
                # 获取transcript
                if 'transcript' in first_row:
                    first_transcript = first_row['transcript']
                    if pd.isna(first_transcript):
                        first_transcript = ''
                    
                # 为所有轮次添加测试用例和first_transcript信息
                for case in cases:
                    case['测试用例'] = test_scenario
                    case['first_transcript'] = first_transcript

                if self._is_case_completed(case_rows):
                    skipped_cases += 1
                    completed_turns += len(cases)
                    completed_cases += 1
                    progress_display.update_progress(len(cases))
                    results.append(self._create_skipped_result(case_rows, case_id))
                    continue

                print(f"\n执行用例 {case_id}:")
                conversation_history = []
                self._clear_case_results(case_indices, excel_file)

                try:
                    all_turns_successful = True # Flag to track success of all turns for this case_id
                    for index, (case, row_index) in enumerate(zip(cases, case_indices)):

                        # 1. 检查初始加载状态是否失败 (例如音频文件不存在)
                        if self._handle_initial_failure(case, case_id, index, excel_file, row_index, results, progress_display):
                            completed_turns += 1 # 算作已处理
                            all_turns_successful = False # 标记用例组失败
                            break # 跳过此用例组的后续轮次

                        # 2. 处理单个轮次的核心逻辑
                        success, assistant_text, error_message = await self._process_turn(
                            case=case, row_index=row_index, handler=handler,
                            conversation_history=conversation_history,
                            excel_file=excel_file, audio_format=audio_format,
                            tts_cases=tts_cases, voice_cases=voice_cases,
                            case_id=case_id, case_indices=case_indices,
                            cases=cases, index=index,
                            max_retries=max_retries
                        )

                        # 3. 更新进度
                        completed_turns += 1
                        self._update_progress(completed_turns, total_turns, completed_cases, total_cases, start_time)
                        progress_display.update_progress(1)

                        # 4. 处理轮次结果
                        if success:
                            # 更新历史记录供下一轮使用
                            user_transcript = clean_string(case.get('transcript')) # 清理用户输入
                            if user_transcript: # 检查清理后的字符串是否非空
                                conversation_history.append({"role": "user", "type": "text", "text": user_transcript})
                            
                            assistant_text_cleaned = clean_string(assistant_text) # 清理助手输出
                            if assistant_text_cleaned:
                                conversation_history.append({"role": "assistant", "type": "text", "text": assistant_text_cleaned})
                            print(f"第 {index + 1} 轮对话完成")
                            results.append(self._create_success_result(case_rows, case_id))
                        else:
                            # 轮次失败
                            all_turns_successful = False # 标记用例组失败
                            session_id = handler.headers.get("Session-ID")
                            session = SessionManager.create_session_url(session_id)
                            self._handle_failed_case(case_id, case_rows, case_indices, index, excel_file, error_message, session)
                            results.append(self._create_failed_result(case_rows, case_id, index, error_message))
                            break # 停止处理此用例组的后续轮次

                    # ---- 用例组内所有轮次处理完毕 ----
                    # 只有当所有轮次都成功时，才记录整个用例组为成功
                    if all_turns_successful:
                        completed_cases += 1
                        # results.append(self._create_success_result(case_rows, case_id))

                except Exception as e:
                    # 捕获处理用例组时的意外异常 (非轮次内的已知失败)
                    self._handle_case_exception(case_id, case_rows, case_indices, e, excel_file)
                    results.append(self._create_error_result(case_rows, case_id, e))
                    continue

            progress_display.update_progress(complete=True)
            progress_display.stop_progress()
            progress_display.display_results(results)
            progress_display.display_summary(results)

            # --- Calculate final counts from results --- 
            passed_cases = 0
            failed_cases = 0
            skipped_cases = 0
            # The results list contains one entry per processed case_id group
            for result in results:
                if result['status'] == TestStatus.SUCCESS:
                    passed_cases += 1
                elif result['status'] == TestStatus.FAILED:
                    failed_cases += 1
                elif result['status'] == TestStatus.SKIPPED:
                    skipped_cases += 1

            # Return the calculated counts
            return {
                'passed': passed_cases,
                'failed': failed_cases,
                'skipped': skipped_cases
            }

        except Exception as e:
            print(f"多轮对话测试执行失败: {str(e)}")
            raise

    def _load_tts_cases(self, excel_file: str) -> Dict:
        """加载TTS用例"""
        tts_cases = {}
        try:
            tts_df = pd.read_excel(excel_file, sheet_name='tts')
            tts_df = tts_df.fillna('') # 将所有NaN值转换为空字符串
            for _, row in tts_df.iterrows():
                case_id = str(row['ID'])
                if case_id not in tts_cases:
                    tts_cases[case_id] = []
                tts_cases[case_id].append({
                    'ID': case_id,
                    '音色': row['音色'],
                    '音量': row['音量'],
                    '语速': row['语速'],
                    'text': row['text'],
                    '音色标签-情绪': row.get('音色标签-情绪', ''),
                    '音色标签-语言&方言': row.get('音色标签-语言&方言', '')
                })
            print(f"已从Excel文件中读取TTS用例: {len(tts_cases)}个")
        except Exception as e:
            print(f"读取TTS sheet失败，将不使用TTS功能: {str(e)}")
        return tts_cases

    def _should_process_case(self, case_id: str, start_id: Optional[str], start_id_found: bool) -> bool:
        """判断是否应该处理当前用例"""
        if start_id and str(case_id) != start_id and not start_id_found:
            print(f"\n跳过ID {case_id} 的用例，等待ID {start_id}")
            return False
        return True

    def _is_case_completed(self, case_rows) -> bool:
        """检查用例是否已完成或需要重新执行
        
        当读取已有测试报告文件时，会检查每个用例的状态:
        - 如果状态为SUCCESS，则认为已完成，跳过执行
        - 如果状态为FAILED、SKIPPED或空，则需要重新执行
        """
        for _, row in case_rows.iterrows():
            # 检查状态字段
            status = row.get('status', '')
            
            # 状态转换为字符串进行比较
            status_str = str(status).strip() if not pd.isna(status) else ''
            test_status = TestStatus.from_string(status_str)
            
            # 只要有任何一行的状态不是 SUCCESS，就需要重新执行
            if test_status != TestStatus.SUCCESS:
                print(f"用例 {case_rows.iloc[0]['ID']} 的第 {_+1} 行状态为 '{status_str}'，需要执行")
                return False
                
        # 所有行都检查通过，可以跳过执行
        print(f"用例 {case_rows.iloc[0]['ID']} 所有轮次已成功执行，将跳过")
        return True

    def _create_skipped_result(self, case_rows, case_id: str) -> Dict:
        """创建跳过的用例结果"""
        return {
            'ID': case_id,
            '功能模块': case_rows.iloc[0].get('功能模块', ''),
            '测试用例': case_rows.iloc[0].get('测试用例', ''),
            'status': TestStatus.SKIPPED,
            'error': '用例已成功执行，跳过重复执行'
        }

    def _clear_case_results(self, case_indices: List[int], excel_file: str):
        """清空用例结果"""
        for row_index in case_indices:
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={
                    row_index: {
                        'output_transcript': '',
                        'audio_mp3': '',
                        'status': '',
                        'test_time': '',
                        'event_id': '',
                        'messages': '',
                        'error_message': '',
                        'realtime首字响应时间': '',
                        'tts首字响应时间': ''
                    }
                }
            )

    def _handle_initial_failure(
        self,
        case: Dict,
        case_id: str,
        index: int,
        excel_file: str,
        row_index: int,
        results: List[Dict],
        progress_display
    ) -> bool:
        """检查并处理初始加载即失败的用例. 返回 True 表示已处理失败, 应跳过后续。"""
        initial_status = case.get('status')
        if isinstance(initial_status, TestStatus) and initial_status == TestStatus.FAILED:
            error_msg = case.get('error_message', '初始状态为FAILED')
            print(f"\n跳过用例 {case_id} 的第 {index + 1} 轮对话: 初始状态为 FAILED (原因: {error_msg})")
            # 确保在Excel中写入失败状态（如果尚未写入）
            data = {
                'output_transcript': '',
                'audio_mp3': '',
                'status': str(TestStatus.FAILED),
                'error_message': error_msg,
                'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'realtime首字响应时间': '',
                'tts首字响应时间': ''
            }
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={row_index: data}
            )
            # 添加到结果摘要，即使它从未真正"执行"
            results.append({
                'ID': case_id,
                '功能模块': case.get('功能模块', ''),
                '测试用例': case.get('测试用例', ''),
                'status': TestStatus.FAILED,
                'error': error_msg
            })
            progress_display.update_progress(1) # 更新进度条
            return True # 表示需要跳过
        return False # 表示状态正常，继续执行

    def _determine_case_type(self, case: Dict) -> Tuple[bool, bool, bool, bool]:
        """确定用例类型"""
        is_tts_type = False
        is_generative_chat = False
        is_direct_tts = False
        is_voice_create = False

        if 'turns' in case and isinstance(case['turns'], str):
            if '生成式闲聊' in case['turns']:
                is_tts_type = True
                is_generative_chat = True
                print(f"检测到生成式闲聊: {case['turns']}")
            elif case['turns'].lower().startswith('tts'):
                is_tts_type = True
                is_direct_tts = True
                print(f"检测到TTS类型对话: {case['turns']}")
            elif case['turns'].startswith('捏音'):
                is_voice_create = True
                print(f"检测到捏音类型对话: {case['turns']}")

        return is_tts_type, is_generative_chat, is_direct_tts, is_voice_create

    async def _process_turn(
        self,
        case: Dict,
        row_index: int,
        handler,
        conversation_history: List[Dict],
        excel_file: str,
        audio_format: str,
        tts_cases: Dict,
        voice_cases: Dict,
        case_id: str,
        case_indices: List[int],
        cases: List[Dict],
        index: int,
        max_retries: int
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """处理单个对话轮次的核心逻辑. 返回 (success, assistant_text, error_message)"""
        
        is_tts_type, is_generative_chat, is_direct_tts, is_voice_create = self._determine_case_type(case)

        try:
            # --- 预处理 --- 
            if is_generative_chat and conversation_history:
                # 修改 case['transcript'] 用于后续处理
                await self._handle_generative_chat(case, conversation_history)

            if is_tts_type and tts_cases and str(case_id) in tts_cases and not is_direct_tts:
                # 处理 TTS 辅助对话: 生成音频并更新 case['sample_audio']
                await self._handle_tts_type(
                    case_id, case, tts_cases, conversation_history,
                    excel_file, audio_format, row_index
                )
                # 注意: _handle_tts_type 本身不直接返回对话结果，它修改 case 供 execute_conversation_turn 使用
                # 如果 _handle_tts_type 内部失败（例如TTS接口错误），它会抛出异常，被下面的 except 捕获

            # --- 执行核心操作 --- 
            if is_voice_create:
                print(f"\n开始执行用例 {case_id} 的第 {index + 1} 轮对话 (类型: 捏音)")
                success, error_msg = await self._handle_voice_create(
                    case_id, case, voice_cases, handler, excel_file,
                    row_index, case_indices, cases, index,
                    conversation_history
                )
                # 捏音不产生 assistant_text
                return success, None, error_msg
            
            elif is_direct_tts:
                print(f"\n开始执行用例 {case_id} 的第 {index + 1} 轮对话 (类型: 直接TTS)")
                success, assistant_text, error_msg = await self._handle_direct_tts(
                    case_id, case, tts_cases, handler, excel_file,
                    row_index, case_indices, index,
                    conversation_history,
                    audio_format=audio_format
                )
                return success, assistant_text, error_msg
            
            else: # 普通对话或 TTS 辅助对话（sample_audio 已被 _handle_tts_type 更新）
                turn_type_desc = "TTS辅助对话" if is_tts_type else "普通对话"
                print(f"\n开始执行用例 {case_id} 的第 {index + 1} 轮对话 (类型: {turn_type_desc})")
                # Pass the history *before* this turn's messages
                success, assistant_text, error_msg = await self.execute_conversation_turn(
                    handler=handler,
                    case=case,
                    conversation_history=conversation_history, # History up to N-1
                    index=index,
                    row_index=row_index,
                    excel_file=excel_file,
                    max_retries=max_retries
                )
                return success, assistant_text, error_msg
        
        except Exception as e:
            # 捕获预处理或核心操作中的意外异常
            error_msg = f"处理第 {index + 1} 轮对话时发生意外错误: {str(e)}"
            print(f"\n[错误] {error_msg}")
            # 可以在这里记录到Excel，或依赖外层处理
            return False, None, error_msg

    async def _handle_generative_chat(self, case: Dict, conversation_history: List[Dict]):
        """处理生成式闲聊"""
        print("\n[DEBUG] 执行生成式闲聊处理流程:")
        print("1. 使用OpenAI生成闲聊内容")
        keywords = ["飞花令", "成语接龙", "猜猜我是谁", "脑筋急转弯", "接故事", "对话互译"]
        matched_keyword = None
        for keyword in keywords:
            if keyword in case["测试用例"]:
                matched_keyword = keyword
                break
                
        # 获取测试用例和开场白（第一轮对话的transcript）
        parameter = case.get('测试用例', '')
        opening_words = case.get('first_transcript', '')
            
        print(f"[DEBUG] 测试用例(parameter): {parameter}")
        print(f"[DEBUG] 开场白(opening_words): {opening_words}")
        
        if matched_keyword:
            chat_text = self.chat_workflow.get_chat_query(conversation_history, matched_keyword, parameter, opening_words)
        else:
            chat_text = self.chat_workflow.get_chat_query(conversation_history, None, parameter, opening_words)

        print(f"生成的闲聊内容: {chat_text}")
        case['transcript'] = chat_text

        print("\n[DEBUG] 已更新case['transcript']为生成的闲聊内容")

    async def _handle_voice_create(
        self,
        case_id: str,
        case: Dict,
        voice_cases: Dict,
        handler,
        excel_file: str,
        row_index: int,
        case_indices: List[int],
        cases: List[Dict],
        index: int,
        conversation_history: List[Dict]
    ) -> Tuple[bool, Optional[str]]:
        """处理捏音类型对话. 返回 (success, error_message)"""
        print("\n[DEBUG] 执行捏音处理流程:")
        print("1. 从'voice' sheet获取捏音参数")

        if not voice_cases or str(case_id) not in voice_cases:
            print(f"\n警告: 未找到ID为{case_id}的捏音用例，跳过该用例")
            return False, None

        voice_case_list = voice_cases[str(case_id)]
        role_api_client = RoleAPI(
            base_url=self.config_manager.get('role.base_url'),
            headers={"Authorization": f"Bearer {self.config_manager.get('role.token')}"}
        )

        voice_workflow = VoiceCreateWorkflow(
            voice_api_client=handler,
            role_api_client=role_api_client
        )

        try:
            # 注意：process_voice_cases 现在只返回 bool
            success = await voice_workflow.process_voice_cases(
                voice_case_list=voice_case_list,
                case=case,
                excel_file=excel_file,
                row_index=row_index,
                case_indices=case_indices,
                cases=cases,
                index=index,
                audio_output_dir=self.audio_output_dir
            )

            if success:
                # 捏音成功，但没有对话文本产生，错误信息为空
                return True, None
            else:
                # process_voice_cases 内部应该已经记录了具体错误，这里返回通用失败信息
                return False, "捏音或角色保存失败"

        except Exception as e:
            error_msg = f"处理捏音时发生意外错误: {str(e)}"
            print(f"\n[错误] {error_msg}")
            # 记录到Excel
            self._handle_case_exception(case_id, pd.DataFrame([case]), [row_index], e, excel_file)
            return False, error_msg

    async def _handle_direct_tts(
        self,
        case_id: str,
        case: Dict,
        tts_cases: Dict,
        handler,
        excel_file: str,
        row_index: int,
        case_indices: List[int],
        index: int,
        conversation_history: List[Dict],
        audio_format: str
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """处理直接TTS类型对话. 返回 (success, assistant_text, error_message)"""
        print("\n[DEBUG] 执行直接TTS处理流程:")
        print("1. 从'tts' sheet获取文本内容")

        if not tts_cases or str(case_id) not in tts_cases:
            error_msg = f"未找到ID为{case_id}的TTS用例，跳过该用例"
            print(f"\n警告: {error_msg}")
            return False, None, error_msg

        tts_case = tts_cases[str(case_id)][0]
        print(f"\n[DEBUG] 找到ID为{case_id}的TTS用例 (来自tts sheet): {tts_case}")

        emotion, language = self._process_tts_params(tts_case)

        # 确定 voice_id：优先使用当前 'realtime' case 中的 role_id (如果它有效且不是默认值)
        # 这允许前一个捏音步骤设置的 role_id 被用于 TTS
        current_turn_role_id_from_realtime_case = case.get('role_id')
        # 清理从 'realtime' case 中获取的 role_id (处理 None, pd.NA, 空白字符)
        cleaned_current_turn_role_id = clean_string(current_turn_role_id_from_realtime_case)
        
        # 默认使用 'tts' sheet 中的 '音色'
        final_voice_id = tts_case['音色'] 

        # 如果 'realtime' case 中的 role_id 清理后有效且不是已知的默认值 "yinhejingling",
        # 则认为它是由捏音步骤设置的，应优先使用。
        if cleaned_current_turn_role_id and cleaned_current_turn_role_id != "yinhejingling":
            print(f"[DEBUG] TTS用例 {case_id}: 检测到来自先前捏音步骤或realtime sheet的 role_id: '{cleaned_current_turn_role_id}', 将优先使用此ID作为TTS的voice_id。")
            final_voice_id = cleaned_current_turn_role_id
        else:
            print(f"[DEBUG] TTS用例 {case_id}: 未检测到有效的优先 role_id (当前realtime sheet role_id: '{current_turn_role_id_from_realtime_case}', 清理后: '{cleaned_current_turn_role_id}'), "
                  f"将使用TTS sheet中的音色: '{tts_case['音色']}'.")

        tts_case_new = {
            "ID": tts_case['ID'],
            "voice_id": final_voice_id,
            'speed_ratio': tts_case['语速'],
            'volume_ratio': tts_case['音量'],
            'emotion': emotion,
            'language': language,
            'text': tts_case['text'],
            'turns': case["turns"]
        }

        try:
            await self.tts_workflow.get_tts_audio(
                test_case=tts_case_new,
                audio_output_dir=self.audio_output_dir,
                excel_file=excel_file,
                row_index=row_index,
                tts_mode=self.tts_mode
            )
        except Exception as e:
            error_msg = f"TTS 音频生成失败: {str(e)}"
            print(f"\n[错误] {error_msg}")
            # 可以在这里选择是否将错误写入Excel，或者依赖外层循环处理
            return False, None, error_msg

        case['audio_path'] = tts_case_new.get('audio_path')
        print(f"\n[DEBUG] 已更新case['audio_path']: {case['audio_path']}")

        if 'audio_path' in tts_case_new and tts_case_new['audio_path']:
            assistant_text = tts_case['text']
            tts_session_id = tts_case_new['session_id']
            
            # 获取TTS首字响应时间
            tts_first_word_time = tts_case_new.get('tts_first_word_time')

            await self._save_realtime_results(
                test_case=case,
                audio_data=b'',
                transcript=assistant_text,
                response={'event_id': str(uuid.uuid4())},
                excel_file=excel_file,
                row_index=row_index,
                request_messages=[], # Direct TTS 没有 request messages
                session_id=tts_session_id,
                first_word_time=None,  # TTS 没有首字回复时间概念
                tts_first_word_time=tts_first_word_time
            )
            # TTS 成功，返回成功状态和 TTS 文本
            return True, assistant_text, None
        else:
            # TTS 生成了 audio_path 但它为空或不存在，视为失败
            error_msg = "TTS未能成功生成或保存音频文件"
            print(f"\n[错误] {error_msg}")
            # 更新Excel状态为失败
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={
                    row_index: {
                        'status': str(TestStatus.FAILED),
                        'error_message': error_msg,
                        'session_id': tts_case_new.get('session_id', ''),
                        'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'realtime首字响应时间': '',
                        'tts首字响应时间': ''
                    }
                }
            )
            return False, None, error_msg

    async def _handle_tts_type(
        self,
        case_id: str,
        case: Dict,
        tts_cases: Dict,
        conversation_history: List[Dict],
        excel_file: str,
        audio_format: str,
        row_index: int = None
    ):
        """处理TTS类型对话"""
        tts_case = tts_cases[str(case_id)][0]

        print("\n[DEBUG] 当前case内容:")
        for k, v in case.items():
            if k != 'sample_audio':
                print(f"  {k}: {v}")
            else:
                print(f"  {k}: <音频数据已省略>")
        print("\n[DEBUG] 当前conversation_history:")
        print(json.dumps(conversation_history, ensure_ascii=False, indent=2))

        text_to_use = case['transcript']
        print(f"2. 将文本内容传给TTS接口生成语音: {text_to_use[:50]}{'...' if len(text_to_use) > 50 else ''}")

        emotion, language = self._process_tts_params(tts_case)

        tts_case_new = {
            "ID": tts_case['ID'],
            "voice_id": tts_case['音色'],
            'speed_ratio': tts_case['语速'],
            'volume_ratio': tts_case['音量'],
            'emotion': emotion,
            'language': language,
            'text': text_to_use,
            'turns': case["turns"]
        }

        await self.tts_workflow.get_tts_audio(
            test_case=tts_case_new, 
            audio_output_dir=self.audio_output_dir,
            excel_file=excel_file,
            row_index=row_index,
            tts_mode=self.tts_mode
        )
        
        # 保存TTS的session_id到主用例中
        if 'session_id' in tts_case_new:
            case['session_id'] = tts_case_new['session_id']

        case['audio_path'] = tts_case_new.get('audio_path')
        print(f"\n[DEBUG] 已更新case['audio_path']: {case['audio_path']}")

        if 'audio_path' in tts_case_new and tts_case_new['audio_path']:
            print("3. 将TTS生成的音频转换为目标格式并进行base64编码")
            mp3_path = tts_case_new['audio_path']
            encoded_audio_data = ""
            try:
                if audio_format == 'ogg':
                    print(f"[DEBUG] 将 MP3 ({mp3_path}) 转换为 OGG")
                    audio_bytes = self.tts_workflow.get_ogg_from_mp3(mp3_path)
                else: # 默认为 pcm
                    print(f"[DEBUG] 将 MP3 ({mp3_path}) 转换为 PCM")
                    audio_bytes = self.tts_workflow.get_pcm_from_mp3(mp3_path)
                
                encoded_audio_data = base64.b64encode(audio_bytes).decode('utf-8')
                print(f"4. 使用生成的 {audio_format.upper()} 音频进行实时对话")
            except Exception as e:
                print(f"[错误] 音频格式转换或编码失败 ({mp3_path} to {audio_format.upper()}): {e}")
                # 可以选择是跳过此轮对话，还是继续（可能导致后续API调用失败）
                # 这里选择继续，但 encoded_audio_data 为空

            case['sample_audio'] = encoded_audio_data

    def _process_tts_params(self, tts_case):
        """处理TTS参数"""
        emotion = tts_case.get('音色标签-情绪', '')
        language = tts_case.get('音色标签-语言&方言', '')
        
        # 处理emotion参数
        if emotion and isinstance(emotion, str):
            emotion = emotion.strip()
            print(f"[DEBUG] 音色标签-情绪参数: {emotion}")
        else:
            emotion = ''
            print("[DEBUG] 不使用音色标签-情绪参数")
            
        # 处理language参数
        if language and isinstance(language, str):
            language = language.strip()
            print(f"[DEBUG] 音色标签-语言&方言参数: {language}")
        else:
            language = ''
            print("[DEBUG] 不使用音色标签-语言&方言参数")
        
        return emotion, language

    def _update_progress(self, completed_turns: int, total_turns: int, completed_cases: int, total_cases: int, start_time: float):
        """更新进度显示"""
        progress = (completed_turns / total_turns) * 100
        filled_length = int(50 * completed_turns // total_turns)
        bar = "█" * filled_length + "-" * (50 - filled_length)
        elapsed = time.time() - start_time
        est_total = elapsed / (completed_turns / total_turns) if completed_turns > 0 else 0
        remaining = est_total - elapsed
        print(f"\r进度: [{bar}] {progress:.1f}% 已完成: {completed_cases}/{total_cases} 用例 ({completed_turns}/{total_turns} 轮) 预计剩余时间: {remaining/60:.1f}分钟", end="")

    def _handle_failed_case(self, case_id: str, case_rows, case_indices: List[int], index: int, excel_file: str, error_message: Optional[str] = None, session: Optional[str] = None):
        """处理失败的用例 (更新Excel)"""
        fail_reason = error_message or f"第 {index + 1} 轮失败"
        data_dict = {
            'status': str(TestStatus.FAILED),
            'error_message': fail_reason, # 使用具体的错误信息
            'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'realtime首字响应时间': '',
            'tts首字响应时间': ''
        }
        if session:
            data_dict['session'] = session

        for idx in case_indices: # 标记整个用例组的所有行为失败
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={
                    idx: data_dict
                }
            )

    def _create_failed_result(self, case_rows, case_id: str, index: int, error_message: Optional[str] = None) -> Dict:
        """创建失败的用例结果 (用于内存中的 results 列表)"""
        fail_reason = error_message or f"第 {index + 1} 轮失败"
        return {
            'ID': case_id,
            '功能模块': case_rows.iloc[0].get('功能模块', ''),
            '测试用例': case_rows.iloc[0].get('测试用例', ''),
            'status': TestStatus.FAILED,
            'error': fail_reason # 使用具体的错误信息
        }

    def _create_success_result(self, case_rows, case_id: str) -> Dict:
        """创建成功的用例结果"""
        return {
            'ID': case_id,
            '功能模块': case_rows.iloc[0].get('功能模块', ''),
            '测试用例': case_rows.iloc[0].get('测试用例', ''),
            'status': TestStatus.SUCCESS,
            'error': ''
        }

    def _handle_case_exception(self, case_id: str, case_rows, case_indices: List[int], error: Exception, excel_file: str):
        """处理用例异常"""
        print(f"\n用例 {case_id} 执行失败: {str(error)}")
        for row_index in case_indices:
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={
                    row_index: {
                        'status': str(TestStatus.FAILED),
                        'error_message': str(error),
                        'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'realtime首字响应时间': '',
                        'tts首字响应时间': ''
                    }
                }
            )

    def _create_error_result(self, case_rows, case_id: str, error: Exception) -> Dict:
        """创建错误用例结果"""
        return {
            'ID': case_id,
            '功能模块': case_rows.iloc[0].get('功能模块', ''),
            '测试用例': case_rows.iloc[0].get('测试用例', ''),
            'status': TestStatus.FAILED,
            'error': str(error)
        }
