#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import asyncio
import argparse
import uuid
import shutil
import pandas as pd
import json
import base64
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import get_config_manager
from utils.test_status import TestStatus
from workflow.realtime_api import RealtimeEventHandler
from workflow.realtime_workflow import ProgressRealtimeWorkflow
from utils.merge_results import merge_sheets # Import the function directly
from utils.data_handler import clean_string, get_cleaned_value_from_row # Import the clean_string and get_cleaned_value_from_row functions


# 初始化配置管理器
config_manager = get_config_manager()

class MultiRealtimeRunner:
    """多轮对话测试执行器"""

    def __init__(self, excel_file, audio_dir=None, max_history_turns=None, start_id=None, username=None, output_dir=None, continue_testing=False, audio_format='pcm', max_retries=1, no_audio_transcript=False, tts_mode='sentence'):
        if excel_file is None:
            raise ValueError("必须提供Excel文件路径")

        # 使用配置中的值或传入的值
        self.max_history_turns = max_history_turns or config_manager.get('max_history_turns', 5)
        self.concurrency_limit = config_manager.get('concurrency_limit', 1)
        self.semaphore = asyncio.Semaphore(self.concurrency_limit)
        self.username = username
        self.output_dir = output_dir
        self.continue_testing = continue_testing
        self.audio_format = audio_format.lower() # 保存音频格式
        self.max_retries = max_retries # 保存最大重试次数
        self.no_audio_transcript = no_audio_transcript
        self.tts_mode = tts_mode

        # 保存起始ID
        self.start_id = start_id
        if self.start_id:
            print(f"\n将从 ID {self.start_id} 开始运行测试，跳过之前的用例")

        # 检查提供的excel文件是否存在
        if not os.path.exists(excel_file):
            raise FileNotFoundError(f"Excel文件不存在: {excel_file}")

        # 保存原始Excel文件路径和目录
        self.original_excel_path = os.path.abspath(excel_file)
        self.original_excel_dir = os.path.dirname(self.original_excel_path)

        # 获取Excel文件名（不含扩展名）
        excel_basename = os.path.basename(excel_file)
        excel_name_without_ext = os.path.splitext(excel_basename)[0]
        self.case_name = excel_name_without_ext

        # 设置音频文件目录（输入目录）
        if audio_dir:
            # 如果提供了音频目录，使用提供的目录
            self.audio_dir = os.path.abspath(audio_dir)
            # 检查音频目录是否存在
            if not os.path.exists(self.audio_dir):
                print(f"警告：指定的音频目录不存在: {self.audio_dir}")
                print("将创建该目录...")
                os.makedirs(self.audio_dir, exist_ok=True)
        else:
            # 如果没有提供音频目录，使用默认的与Excel文件同名的目录
            self.audio_dir = os.path.join(self.original_excel_dir, excel_name_without_ext)
            # 检查默认音频目录是否存在
            if not os.path.exists(self.audio_dir):
                print(f"警告：默认音频目录不存在: {self.audio_dir}")

        # 设置输出目录
        if self.output_dir:
            # 使用用户指定的输出目录
            report_dir = os.path.abspath(self.output_dir)
        else:
            # 使用默认的reports目录
            report_dir = os.path.join(Path(__file__).parent, 'reports')
        
        # 确保输出目录存在
        os.makedirs(report_dir, exist_ok=True)
        
        # 创建音频输出目录结构: <output_dir>/<用例名>/
        self.audio_output_dir = os.path.join(report_dir, self.case_name)
        os.makedirs(self.audio_output_dir, exist_ok=True)

        # 处理测试报告文件
        self.report_file_name = f'{excel_name_without_ext}_testReport.xlsx'
        self.excel_file = os.path.join(report_dir, self.report_file_name)
        
        print("=============== 测试执行参数 ===============")
        if self.continue_testing:
            # 继续测试模式下，检查是否存在对应的测试报告文件
            if os.path.exists(self.excel_file):
                print(f"继续执行模式: 找到测试报告文件: {self.excel_file}")
                print(f"使用Excel文件: {self.excel_file}")
            else:
                print(f"继续执行模式: 未找到测试报告，将创建新文件: {self.excel_file}")
                # 复制用户提供的Excel文件到报告目录
                shutil.copyfile(excel_file, self.excel_file)
                print(f"使用Excel文件: {excel_file}")
        else:
            # 非继续测试模式，创建新的测试报告文件
            # 复制用户提供的Excel文件到报告目录
            if os.path.exists(self.excel_file):
                backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_file = os.path.join(report_dir, f'{excel_name_without_ext}_testReport_{backup_timestamp}.xlsx')
                shutil.copyfile(self.excel_file, backup_file)
                print(f"已存在的测试报告备份为: {backup_file}")
            
            shutil.copyfile(excel_file, self.excel_file)
            print(f"创建新的测试报告: {self.excel_file}")
            print(f"使用测试用例文件: {excel_file}")

        
        print(f"音频文件目录: {self.audio_dir}")
        print(f"报告输出目录: {report_dir}")
        print(f"音频输出目录: {self.audio_output_dir}")
        print(f"输入音频格式: {self.audio_format}") 

        # 不再在初始化时读取TTS sheet，而是在执行时读取

        # 构建 Realtime API 的 base_url
        base_url_template = config_manager.get('realtime.base_url')
        realtime_base_url = base_url_template.format(audio_format=self.audio_format)
        print(f"Realtime API Base URL: {realtime_base_url}")

        # 初始化API客户端
        self.api_client = RealtimeEventHandler(
            base_url=realtime_base_url, # 使用动态构建的 URL
            headers={
                "Authorization": f"Bearer {config_manager.get('realtime.token')}",
                "OpenAI-Beta": "realtime=v1"
            },
            username=self.username
        )

        print(f"历史对话最大保留轮数: {self.max_history_turns}")
        print("============================================")

        # 初始化工作流
        self.workflow = ProgressRealtimeWorkflow(max_history_turns=self.max_history_turns)

    def _load_voice_cases(self):
        """从Excel文件加载捏音用例"""
        self.voice_cases = {}
        try:
            voice_df = pd.read_excel(self.excel_file, sheet_name='voice', engine='openpyxl')
            voice_df = voice_df.fillna('') # 将所有NaN值转换为空字符串
            for original_index, row in voice_df.iterrows():
                case_id = str(row['ID'])
                if case_id not in self.voice_cases:
                    self.voice_cases[case_id] = []

                case = {
                    'ID': case_id,
                    'voice_id': row['voice_id'],
                    'name': row['name'],
                    'gender': row['gender'],
                    'style': row['style'],
                    'text_style': row['text_style'],
                    'original_voice_sheet_row_index': original_index
                }

                # 处理messages字段
                if 'messages' in row:
                    case['messages'] = row.get('messages', '')

                self.voice_cases[case_id].append(case)
            print(f"\n已从Excel文件中读取捏音用例: {len(self.voice_cases)}个")
        except Exception as e:
            print(f"\n警告: 读取voice sheet失败，将不使用捏音功能: {str(e)}")
            self.voice_cases = {}
        return self.voice_cases

    def _load_realtime_cases(self):
        """从Excel文件加载realtime用例"""
        df = pd.read_excel(self.excel_file, sheet_name='realtime', engine='openpyxl')
        df = df.fillna('') # 将所有NaN值转换为空字符串
        cases = []
        for _, row in df.iterrows():
            # 创建基础case对象，包含共同字段
            case = {
                'ID': row['ID'],
                '功能模块': get_cleaned_value_from_row(row, '功能模块', ''),
                '测试用例': get_cleaned_value_from_row(row, '测试用例', ''),
                'turn_id': str(uuid.uuid4()),
                'role_id': get_cleaned_value_from_row(row, 'role_id', "yinhejingling"),
                'transcript': str(row.get('transcript', '')).strip(),
                'language': get_cleaned_value_from_row(row, 'language', '中文'),
                'pattern': get_cleaned_value_from_row(row, 'pattern', 'normal'),
                "语速": row.get('语速', None),
                "音量": row.get('音量', None),
                'sample_audio': "",
                'turns': get_cleaned_value_from_row(row, 'turns', "1")
            }
            
            # 处理messages字段
            if 'messages' in row and not pd.isna(row['messages']) and row['messages'].strip() != '':
                try:
                    messages_content = json.loads(row['messages'])
                    case['messages'] = messages_content
                except Exception as e:
                    print(f"解析messages列内容失败: {str(e)}")
                    case['messages'] = row.get('messages', '')
            
            # 检查是否是tts类型或捏音类型的对话
            turns_value = row.get('turns', '')
            if isinstance(turns_value, str) and (turns_value.lower().startswith('tts') or '生成式闲聊' in turns_value or turns_value.startswith('捏音')):
                if turns_value.startswith('捏音'):
                    print(f"检测到捏音类型对话: ID {case['ID']} {turns_value}, 将跳过音频文件检查")
                else:
                    print(f"检测到TTS类型对话: ID {case['ID']} {turns_value}, 将跳过音频文件检查")
                
                # 添加TTS/捏音特有字段
                case['turns'] = turns_value
                cases.append(case)
                continue

            # 非TTS类型对话，需要检查音频文件
            text = str(row.get('transcript', '')) 
            text = re.sub(r'[\x00-\x1F\u200B-\u200D\uFEFF]', '', text)
            text = text.replace(' ', '_')
            text = text.replace("'", '')
            # 根据选择的音频格式确定文件扩展名
            audio_extension = f".{self.audio_format}"
            audio_path = os.path.join(self.audio_dir, f"{text}{audio_extension}")

            # 检查文件是否存在
            if not os.path.exists(audio_path):
                print(f"警告：音频文件 {audio_path} 不存在，跳过该测试用例")
                # 标记为skipped状态
                case['status'] = TestStatus.FAILED
                case['error_message'] = '音频文件不存在'
                cases.append(case)
                continue

            # 读取音频文件并进行base64编码
            try:
                with open(audio_path, 'rb') as f:
                    audio_bytes = f.read()
                base64_audio = base64.b64encode(audio_bytes).decode('utf-8')
            except Exception as e:
                print(f"音频文件处理失败: {audio_path}: {str(e)}")
                base64_audio = ""

            # 添加音频数据
            case['sample_audio'] = base64_audio
            cases.append(case)
            
        return cases

    def load_grouped_cases(self):
        """从指定Excel文件加载并分组测试用例"""
        try:
            # 读取voice sheet
            self._load_voice_cases()
            
            # 读取realtime sheet
            cases = self._load_realtime_cases()

            # 按ID分组
            grouped_cases = {}
            for case in cases:
                case_id = case['ID']
                if case_id not in grouped_cases:
                    grouped_cases[case_id] = []
                grouped_cases[case_id].append(case)

            return grouped_cases

        except Exception as e:
            print(f"Excel文件加载失败: {str(e)}")
            raise

    async def run_tests(self) -> Dict[str, int]:
        """执行所有测试用例并返回统计结果"""
        grouped_cases = self.load_grouped_cases()

        handler = self.api_client

        results_summary = {
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'error': False # Default to no error
        }

        try:
            results_summary = await self.workflow.execute_multi_realtime_sequence(
                handler=handler,
                grouped_cases=grouped_cases,
                excel_file=self.excel_file,
                audio_output_dir=self.audio_output_dir,
                max_retries=self.max_retries, # 传递存储的 max_retries
                start_id=self.start_id,
                voice_cases=self.voice_cases,
                audio_format=self.audio_format,
                no_audio_transcript=self.no_audio_transcript,
                tts_mode=self.tts_mode
            )
            print(f"\n结果已保存到: {self.excel_file}")
            return results_summary # <-- Explicitly return the result

        except Exception as e:
            print(f"MultiRealtimeRunner.run_tests 执行失败: {str(e)}")
            # Return a dictionary indicating failure when an exception occurs here
            results_summary = {
                'passed': 0,
                'failed': -1, # Indicate internal runner failure
                'skipped': 0,
                'error': True
            }
            # Optionally re-raise if the batch runner should stop
            # raise 
            return results_summary # Return error summary

        finally:
            try:
                # Ensure connection is closed even if errors occur
                if handler:
                    await handler.close()
            except Exception as close_err:
                print(f"关闭连接时发生错误: {close_err}")
            # Ensure VoiceCreateWorkflow connections are handled if necessary
            # (Assuming VoiceCreateWorkflow might have its own cleanup)

            # Call merge_sheets function directly
            if not os.path.exists(self.excel_file):
                print(f"  报告文件 {self.excel_file} 不存在，跳过合并步骤。")
            else:
                try:
                    # Directly call the imported function
                    merge_sheets(self.excel_file)
                    # The merge_sheets function prints its own success/failure messages now
                    # print(f"  结果合并完成 for {self.excel_file}.") 
                except Exception as merge_err:
                    print(f"  调用结果合并函数时发生意外错误 for {self.excel_file}: {merge_err}")

async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='多轮对话测试执行工具')
    parser.add_argument('excel', type=str, help='Excel文件路径，包含测试用例（必需）')
    parser.add_argument('--audio-dir', '-a', type=str, help='音频输入文件目录，默认为与Excel文件同名的目录')
    parser.add_argument('--max-history', '-m', type=int, help='历史对话最大保留轮数，默认为配置中的值')
    parser.add_argument('--config', '-c', type=str, help='配置文件路径，默认为不使用配置文件')
    parser.add_argument('--start-id', '-s', type=str, help='指定从哪个ID的用例开始运行，该ID之前的用例将被跳过')
    parser.add_argument('--username', '-u', type=str, help='用户名，用于生成session-id')
    parser.add_argument('--output-dir', '-o', type=str, help='输出目录路径，默认为脚本所在目录下的reports目录')
    parser.add_argument('--continue', action='store_true', help='如果已存在测试报告，则根据测试报告内容继续执行失败或未执行的用例')
    parser.add_argument('--audio-format', type=str, default='ogg', choices=['pcm', 'ogg'], help='输入音频文件的格式 (pcm 或 ogg)，默认为 ogg')
    parser.add_argument('--max-retries', type=int, default=1, help='单轮对话最大重试次数 (默认为 1)')
    parser.add_argument('--no-audio-transcript', action='store_true', help='不传递语音文本内容，发送空transcript和language')
    parser.add_argument('--tts-mode', type=str, default='sentence', choices=['sentence', 'slice'], help='TTS文本发送模式: sentence(整句) 或 slice(切片)，默认为slice')
    args = parser.parse_args()

    # 如果提供了配置文件，重新初始化配置管理器
    global config_manager
    if args.config:
        config_manager = get_config_manager(args.config, force_reload=True)

    # 检查是否需要继续执行已有测试报告
    continue_testing = getattr(args, 'continue', False)
    
    # 获取Excel文件的绝对路径
    excel_path = os.path.abspath(args.excel)
    
    # 创建并运行测试
    runner = MultiRealtimeRunner(
        excel_file=excel_path,
        audio_dir=args.audio_dir,
        max_history_turns=args.max_history,
        start_id=args.start_id,
        username=args.username,
        output_dir=args.output_dir,
        continue_testing=continue_testing,
        audio_format=args.audio_format, # 传递音频格式
        max_retries=args.max_retries, # 传递最大重试次数
        no_audio_transcript=args.no_audio_transcript, # 新增参数
        tts_mode=args.tts_mode # 新增参数
    )
    await runner.run_tests()

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
