#!/usr/bin/env python3
"""
测试performance sheet功能的简单脚本
"""

import pandas as pd
import os
import tempfile
from utils.merge_results import create_performance_sheet

def create_test_excel():
    """创建一个测试用的Excel文件"""
    # 创建测试数据
    test_data = {
        'ID': [1, 2, 3, 4, 5, 6, 7, 8],
        '功能模块': ['realtime', 'realtime', 'tts', 'tts', 'voice', 'voice', 'realtime', 'tts'],
        '测试用例': ['测试1', '测试2', '测试3', '测试4', '测试5', '测试6', '测试7', '测试8'],
        'turns': ['1', '2', 'tts1', 'tts2', '捏音1', '捏音2', '3', 'tts3'],
        'status': ['SUCCESS', 'SUCCESS', 'SUCCESS', 'SUCCESS', 'SUCCESS', 'SUCCESS', 'SUCCESS', 'SUCCESS'],
        'realtime首字响应时间': ['1200', '1500', '', '', '', '', '1100', ''],
        'tts首字响应时间': ['', '', '800', '900', '', '', '', '750'],
        'voice首字响应时间': ['', '', '', '', '2000', '2200', '', '']
    }
    
    summary_df = pd.DataFrame(test_data)
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_file.close()
    
    # 写入Excel文件
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='summary', index=False)
    
    return temp_file.name

def test_performance_sheet():
    """测试performance sheet功能"""
    print("开始测试performance sheet功能...")
    
    # 创建测试Excel文件
    test_file = create_test_excel()
    print(f"创建测试文件: {test_file}")
    
    try:
        # 调用create_performance_sheet函数
        create_performance_sheet(test_file)
        
        # 验证结果
        xls = pd.ExcelFile(test_file, engine='openpyxl')
        
        if 'performance' in xls.sheet_names:
            print("✅ Performance sheet创建成功")
            
            # 读取performance sheet
            performance_df = pd.read_excel(xls, sheet_name='performance', engine='openpyxl')
            print(f"Performance sheet包含 {len(performance_df)} 行数据")
            
            # 显示结果
            print("\nPerformance统计结果:")
            print(performance_df.to_string(index=False))
            
            # 验证预期结果
            expected_metrics = [
                'realtime首字响应平均时间',
                'tts首字响应平均时间', 
                'voice首字响应平均时间'
            ]
            
            actual_metrics = performance_df['指标'].tolist()
            
            for metric in expected_metrics:
                if metric in actual_metrics:
                    print(f"✅ 找到指标: {metric}")
                else:
                    print(f"❌ 缺少指标: {metric}")
            
            # 验证计算结果
            realtime_row = performance_df[performance_df['指标'] == 'realtime首字响应平均时间']
            if not realtime_row.empty:
                avg_val = realtime_row['平均值'].iloc[0]
                sample_count = realtime_row['样本数量'].iloc[0]
                print(f"✅ Realtime平均时间: {avg_val}ms (样本数: {sample_count})")
                # 预期: (1200 + 1500 + 1100) / 3 = 1266.67 ≈ 1267
                if sample_count == 3:
                    print("✅ Realtime样本数量正确")
                else:
                    print(f"❌ Realtime样本数量错误，期望3，实际{sample_count}")
            
            tts_row = performance_df[performance_df['指标'] == 'tts首字响应平均时间']
            if not tts_row.empty:
                avg_val = tts_row['平均值'].iloc[0]
                sample_count = tts_row['样本数量'].iloc[0]
                print(f"✅ TTS平均时间: {avg_val}ms (样本数: {sample_count})")
                # 预期: (800 + 900 + 750) / 3 = 816.67 ≈ 817
                if sample_count == 3:
                    print("✅ TTS样本数量正确")
                else:
                    print(f"❌ TTS样本数量错误，期望3，实际{sample_count}")
            
            voice_row = performance_df[performance_df['指标'] == 'voice首字响应平均时间']
            if not voice_row.empty:
                avg_val = voice_row['平均值'].iloc[0]
                sample_count = voice_row['样本数量'].iloc[0]
                print(f"✅ Voice平均时间: {avg_val}ms (样本数: {sample_count})")
                # 预期: (2000 + 2200) / 2 = 2100
                if sample_count == 2:
                    print("✅ Voice样本数量正确")
                else:
                    print(f"❌ Voice样本数量错误，期望2，实际{sample_count}")
            
            return True
        else:
            print("❌ Performance sheet未创建")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"清理测试文件: {test_file}")

def main():
    """主函数"""
    print("=" * 60)
    print("Performance Sheet功能测试")
    print("=" * 60)
    
    success = test_performance_sheet()
    
    print("=" * 60)
    if success:
        print("✅ 所有测试通过")
    else:
        print("❌ 测试失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
